import { Component, Input, OnInit } from '@angular/core';
import { ManagedAccountService } from '../../../managed-account.service';
import { GetManagedCapTableValuesQuery } from '../../../models/managed-cap-table.model';

@Component({
  selector: 'app-flat-data-table',
  templateUrl: './flat-data-table.component.html',
  styleUrls: ['./flat-data-table.component.scss']
})
export class FlatDataTableComponent implements OnInit {

  @Input() periodId: number = 36;
  @Input() managedAccountId: string = 'EE1156FD-4BDB-4636-AC0A-8C1F7563AB42';
  @Input() moduleName: string = 'ManagedCapTable1';
  @Input() isMonthly: boolean =  true;
  @Input() isQuarterly: boolean = false;
  @Input() isAnnually: boolean = false;

  capTableData: any[] = [];
  loading: boolean = false;

  constructor(private managedAccountService: ManagedAccountService) {}

  ngOnInit(): void {
    this.loadCapTableValues();
  }

  loadCapTableValues(): void {
    if (!this.managedAccountId || !this.moduleName) {
      return;
    }

    this.loading = true;

    const query: GetManagedCapTableValuesQuery = {
      managedAccountId: this.managedAccountId,
      periodId: this.periodId,
      isMonthly: this.isMonthly,
      isQuarterly: this.isQuarterly,
      isAnnually: this.isAnnually,
      moduleName: this.moduleName
    };

    this.managedAccountService.getManagedCapTableValues(query).subscribe({
      next: (response) => {
        this.capTableData = response;
        this.loading = false;
      },
      error: (error) => {
        console.error('Error loading managed cap table values:', error);
        this.loading = false;
      }
    });
  }
}
