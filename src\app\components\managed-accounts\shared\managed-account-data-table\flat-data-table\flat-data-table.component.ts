import { Component, Input, OnInit } from '@angular/core';
import { ManagedAccountService } from '../../../managed-account.service';
import { GetManagedCapTableValuesQuery } from '../../../models/managed-cap-table.model';
import { KpiInfo, NumberDecimalConst } from 'src/app/common/constants';
import { isNumeric } from "@angular-devkit/architect/node_modules/rxjs/internal/util/isNumeric";

@Component({
  selector: 'app-flat-data-table',
  templateUrl: './flat-data-table.component.html',
  styleUrls: ['./flat-data-table.component.scss']
})
export class FlatDataTableComponent implements OnInit {

  @Input() periodId: number = 36;
  @Input() managedAccountId: string = 'EE1156FD-4BDB-4636-AC0A-8C1F7563AB42';
  @Input() moduleName: string = 'ManagedCapTable1';
  @Input() isMonthly: boolean =  true;
  @Input() isQuarterly: boolean = false;
  @Input() isAnnually: boolean = false;

  // Grid data properties
  tableColumns: any[] = [];
  tableResult: any[] = [];
  loading: boolean = false;

  // Constants for templates
  kpiInfo = KpiInfo;
  NumberDecimalConst = NumberDecimalConst;

  // Virtual scrolling configuration
  public virtual: any = {
    itemHeight: 32,
    pageSize: 20
  };

  constructor(private managedAccountService: ManagedAccountService) {}

  ngOnInit(): void {
    this.loadCapTableValues();
  }

  loadCapTableValues(): void {
    if (!this.managedAccountId || !this.moduleName) {
      return;
    }

    this.loading = true;

    const query: GetManagedCapTableValuesQuery = {
      managedAccountId: this.managedAccountId,
      periodId: this.periodId,
      isMonthly: this.isMonthly,
      isQuarterly: this.isQuarterly,
      isAnnually: this.isAnnually,
      moduleName: this.moduleName
    };

    this.managedAccountService.getManagedCapTableValues(query).subscribe({
      next: (response) => {
        if (response && response.headers && response.rows) {
          // Process headers - add dir property for sorting
          response.headers.forEach((element: any) => {
            element.dir = null;
          });
          this.tableColumns = response.headers || [];
          this.tableResult = response.rows || [];
        } else {
          this.clearData();
        }
        this.loading = false;
      },
      error: (error) => {
        console.error('Error loading managed cap table values:', error);
        this.clearData();
        this.loading = false;
      }
    });
  }

  /**
   * Clears the data in the component.
   */
  clearData(): void {
    this.loading = false;
    this.tableColumns = [];
    this.tableResult = [];
  }

  /**
   * Checks if the given value is a number.
   * @param str - The value to check.
   * @returns `true` if the value is a number, `false` otherwise.
   */
  isNumberCheck(str: any): boolean {
    return isNumeric(str);
  }

  /**
   * Gets the value from a row for a specific field.
   * @param row - The row data.
   * @param field - The field name.
   * @returns The field value.
   */
  getValue(row: any, field: string): any {
    return row[field];
  }
}
