@import "../../../../../../variables";

.managed-cap-table-container {
  width: 100%;
  height: 100%;

  .loading-container {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 200px;

    .spinner-border {
      width: 3rem;
      height: 3rem;
      color: $nep-primary;
    }
  }

  .cap-table-grid-container {
    width: 100%;

    .custom-kendo-cap-table-grid {
      border: 1px solid $nep-divider;
      border-radius: 0.25rem;

      .header-icon-wrapper {
        width: 100%;
        display: flex;
        align-items: center;

        .TextTruncate {
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
          font-weight: 600;
          color: $nep-dark-black;
        }
      }

      .content {
        padding: 0.5rem;

        .showToolTip {
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }

        .bold-text {
          font-weight: 600;
          color: $nep-dark-black;
        }

        .headerKpi {
          background-color: $nep-light-grey;
          font-weight: 700;
        }

        .pl-3 {
          padding-left: 1rem;
        }

        .table-data-right {
          text-align: right;
        }

        .float-right {
          float: right;
          width: 100%;
          text-align: right;
        }

        .float-left {
          float: left;
          width: 100%;
          text-align: left;
        }

        .left-align {
          text-align: left;
        }

        .w100Percent {
          width: 100% !important;
        }

        .drop-above {
          position: relative;
        }
      }
    }

    .empty-state {
      display: flex;
      justify-content: center;
      align-items: center;
      height: 200px;
      color: $nep-text-grey;
      font-size: 1rem;
    }
  }
}

// Responsive design
@media (max-width: 768px) {
  .managed-cap-table-container {
    .cap-table-grid-container {
      .custom-kendo-cap-table-grid {
        font-size: 0.875rem;
      }
    }
  }
}